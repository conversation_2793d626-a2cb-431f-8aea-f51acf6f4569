import 'dart:async';
import 'dart:math';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:masterg/blocs/auth_bloc.dart';
import 'package:masterg/blocs/bloc_manager.dart';
import 'package:masterg/blocs/home_bloc.dart';
import 'package:masterg/data/api/api_service.dart';
import 'package:masterg/data/models/response/auth_response/bottombar_response.dart';
import 'package:masterg/data/models/response/auth_response/user_session.dart';
import 'package:masterg/local/pref/Preference.dart';
import 'package:masterg/pages/auth_pages/new_language_screen.dart';
import 'package:masterg/pages/custom_pages/alert_widgets/alerts_widget.dart';
import 'package:masterg/pages/custom_pages/custom_widgets/NextPageRouting.dart';
import 'package:masterg/pages/ghome/home_page.dart';
import 'package:masterg/utils/Log.dart';
import 'package:masterg/utils/Styles.dart';
import 'package:masterg/utils/config.dart';
import 'package:masterg/utils/constant.dart';
import 'package:masterg/utils/resource/colors.dart';
import 'package:masterg/utils/utility.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:video_player/video_player.dart';
import '../../utils/notification_helper.dart';
import '../preboarding_pages/preboarding_page.dart';

class EntryAnimationPage extends StatefulWidget {
  final String? intialRoute;
  final Widget? routeWidget;
  const EntryAnimationPage({Key? key, this.intialRoute, this.routeWidget})
      : super(key: key);

  @override
  _EntryAnimationPageState createState() => _EntryAnimationPageState();
}

class _EntryAnimationPageState extends State<EntryAnimationPage> {
  final random = Random();
  List<Menu>? menuList;

  late NotificationHelper _notificationHelper;
  VideoPlayerController? videocontroller;
  @override
  void initState() {
    super.initState();

    _notificationHelper = NotificationHelper.getInstance(context);
    Utility.waitFor(1).then((value) {
      UserSession();
      _notificationHelper.setFcm();
    });

    if (APK_DETAILS['package_name'] == 'com.singularis.mescdigilibrary') {
      videocontroller =
          VideoPlayerController.asset('assets/images/ebook/splash.mp4')
            ..initialize().then((_) {
              videocontroller?.play();
              setState(() {
                // VideoPlayerController is initialized
              });
            });

      // Add a listener to check when the video has ended
      videocontroller?.addListener(() {
        if (videocontroller?.value.position ==
            videocontroller?.value.duration) {
          // Video has ended
          _getAppVersion();
        }
      });
    } else {
      _getAppVersion();
    }
  }

  int waitFor = 1000;

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String imagePath = 'assets/images/splash/${APK_DETAILS['splash_image']}';
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: BlocManager(
          initState: (BuildContext context) {},
          child: MultiBlocListener(
            listeners: [
              BlocListener<AuthBloc, AuthState>(
                listener: (BuildContext context, state) {
                  if (state is AppVersionState) _handleResponse(state);
                },
              ),
              BlocListener<HomeBloc, HomeState>(
                listener: (BuildContext context, state) {
                  if (state is GetBottomBarState) {
                    _handelBottomNavigationBar(state);
                  }
                  if (state is PortfolioState) {
                    handlePortfolioState(state);
                  }
                },
              ),
            ],
            child: Container(
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      gradient: APK_DETAILS['package_name'] ==
                              "com.singulariswow.aid"
                          ? LinearGradient(
                              begin: Alignment.bottomCenter,
                              end: Alignment.topCenter,
                              colors: [Color(0xff0D1119), Color(0xff0D1119)],
                            )
                          : LinearGradient(
                              begin: Alignment.bottomCenter,
                              end: Alignment.topCenter,
                              colors: [
                                ColorConstants().gradientRight(),
                                ColorConstants().gradientLeft(),
                              ],
                            ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // if (APK_DETAILS['package_name'] == 'com.singulariswow.mec')
                        APK_DETAILS['package_name'] != 'com.singularis.mesc' &&
                                APK_DETAILS['package_name'] !=
                                    'com.singulariswow'
                            ? SizedBox(
                                height: height(context) / 2 - 50,
                              )
                            : SizedBox(),
                        Center(
                          child: Stack(
                            children: [
                              //CustomCard("Entry.scale()"),
                              imagePath.split('.').last == 'svg'
                                  ? SvgPicture.asset(
                                      imagePath,
                                      height: 70,
                                      allowDrawingOutsideViewBox: true,
                                    )
                                  : Image.asset(
                                      imagePath,
                                      height: 150,
                                      width: 150,
                                    ),
                            ],
                          ),
                        ),
                        if (APK_DETAILS['package_name'] == 'com.singulariswow.mec' ||
                            APK_DETAILS['package_name'] ==
                                "com.singulariswow.aid" ||
                            APK_DETAILS['package_name'] ==
                                'com.singularis.jumeira')
                          Expanded(child: SizedBox()),
                        if (APK_DETAILS['package_name'] == 'com.singulariswow.mec' ||
                            APK_DETAILS['package_name'] ==
                                "com.singulariswow.aid" ||
                            APK_DETAILS['package_name'] ==
                                'com.singularis.jumeira')
                          Container(
                              // height: 50,
                              padding: const EdgeInsets.only(bottom: 30),
                              child: Center(
                                  child: Column(
                                children: [
                                  Text(
                                    'powered_by',
                                    style: Styles.regularWhite(),
                                  ).tr(),
                                  SvgPicture.asset(
                                    'assets/images/singularis.svg',
                                    fit: BoxFit.cover,
                                    color: ColorConstants.WHITE,
                                  )
                                ],
                              ))),
                      ],
                    ),
                  ),
          )),
    );
  }

  void _getAppVersion() {
    BlocProvider.of<AuthBloc>(context)
        .add(AppVersionEvent(deviceType: Utility.getDeviceType().toString()));
  }

  void _handleResponse(AppVersionState state) {
    var loginState = state;
    switch (loginState.apiState) {
      case ApiStatus.LOADING:
        Log.v("Loading....................");
        break;
      case ApiStatus.SUCCESS:
        Log.v(
            "Success.................... set ${state.response?.data?.enablePi}");

        if (state.response?.data?.termsAndConUrl == "" ||
            state.response?.data?.termsAndConUrl == null) {
          Preference.setString(
              Preference.TERMS_AND_CON_URL, '${APK_DETAILS['policy_url']}');
        } else
          Preference.setString(Preference.TERMS_AND_CON_URL,
              '${state.response?.data?.termsAndConUrl}');

        if (kIsWeb)
          _moveToNext();
        else
          PackageInfo.fromPlatform().then((PackageInfo packageInfo) async {
            //String appName = packageInfo.appName;
            //String packageName = packageInfo.packageName;
            //String version = packageInfo.version;
            String buildNumber = packageInfo.buildNumber;
            String updateUrl = state.response?.data?.deviceType == 1
                ? 'https://play.google.com/store/apps/details?id=' +
                    APK_DETAILS['package_name']!
                : 'https://apps.apple.com/us/app/id${APK_DETAILS['appId']}';
            if (isUpdateAvailable(state, buildNumber)) {
              if (state.response!.data!.updateType == 2) {
                AlertsWidget.showCustomDialog(
                    context: context,
                    title: '',
                    text: tr('update_version_text2'),
                    icon: 'assets/images/circle_alert_fill.svg',
                    oKText: '${tr('ok')}',
                    showCancel: false,
                    onOkClick: () async {
                      launchUrl(Uri.parse(updateUrl),
                          mode: LaunchMode.externalApplication);
                      // _moveToNext();
                    });
              } else if (state.response?.data?.updateType == 3) {
                int? lanuageId =
                    Preference.getInt(Preference.APP_LANGUAGE) ?? 1;
                String? appEnglishName =
                    Preference.getString(Preference.APP_ENGLISH_NAME) ?? 'en';
                Preference.clearPref().then((value) async {
                  Preference.setInt(Preference.APP_LANGUAGE, lanuageId);
                  Preference.setString(
                      Preference.APP_ENGLISH_NAME, appEnglishName);
                  AlertsWidget.showCustomDialog(
                      context: context,
                      title: '',
                      text: tr('update_version_text2'),
                      icon: 'assets/images/circle_alert_fill.svg',
                      oKText: '${tr('ok')}',
                      showCancel: false,
                      backonOk: false,
                      onOkClick: () async {
                        launchUrl(Uri.parse(updateUrl),
                            mode: LaunchMode.externalApplication);
                      });
                });
              } else if (Preference.getBool(_getUpdateKey(), def: true)!) {
                Preference.setBool(_getUpdateKey(), false);
                AlertsWidget.showCustomDialog(
                    context: context,
                    title: tr('update_version_title'),
                    text: tr('update_version_text1'),
                    icon: 'assets/images/circle_alert_fill.svg',
                    oKText: '${tr('ok')}',
                    showCancel: true,
                    onOkClick: () {
                      launchUrl(Uri.parse(updateUrl),
                          mode: LaunchMode.externalApplication);

                      _moveToNext();
                    },
                    onCancelClick: () {
                      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
                        _moveToNext();
                      });
                    });
              } else {
                _moveToNext();
              }
            } else {
              _moveToNext();
            }
          });

        break;
      case ApiStatus.ERROR:
        _moveToNext();
        Preference.setString(
            Preference.TERMS_AND_CON_URL, '${APK_DETAILS['policy_url']}');
        FirebaseAnalytics.instance.logEvent(name: 'splash_screen', parameters: {
          "ERROR": '${loginState.error}',
        });
        break;
      case ApiStatus.INITIAL:
        break;
    }
  }

  bool isUpdateAvailable(AppVersionState state, String buildNumber) {
    int vNumber = int.parse(buildNumber);
    int serverNumber =
        int.tryParse('${state.response?.data?.latestVersion}') ?? -1;
    return vNumber < serverNumber;
  }

  _getUpdateKey() {
    return "${Preference.APP_LANGUAGE}_${Utility.convertDateFormat(DateTime.now())}";
  }

  void getBottomNavigationBar() {
    BlocProvider.of<HomeBloc>(context).add((GetBottomNavigationBarEvent()));
    getPortfolio();
  }

  void getPortfolio() {
    BlocProvider.of<HomeBloc>(context).add(PortfolioEvent());
  }


  void handlePortfolioState(PortfolioState state) {
    var portfolioState = state;
    setState(() async {
      switch (portfolioState.apiState) {
        case ApiStatus.LOADING:
          Log.v("PortfolioState Loading...................");
          break;
        case ApiStatus.SUCCESS:

          portfolioState.response = portfolioState.response;

          if ('${portfolioState.response?.data.name}' != '')
            Preference.setString(
                Preference.FIRST_NAME, '${portfolioState.response?.data.name}');
          if (portfolioState.response?.data.image.contains(
                  '${Preference.getString(Preference.PROFILE_IMAGE)}') ==
              true) {
            Preference.setString(Preference.PROFILE_IMAGE,
                '${portfolioState.response?.data.image}');
          }

          Preference.setString(Preference.USER_EMAIL,
              '${portfolioState.response?.data.portfolioSocial.first['email']}');
          Preference.setString(Preference.PHONE,
              '${portfolioState.response?.data.portfolioSocial.first['mob_num']}');

          if ('${portfolioState.response?.data.image}' != '')
            Preference.setString(Preference.PROFILE_IMAGE,
                '${portfolioState.response?.data.image}');

          Preference.setString(Preference.PROFILE_VIDEO,
              '${portfolioState.response?.data.profileVideo}');
          Preference.setInt(Preference.PROFILE_PERCENT,
              portfolioState.response!.data.profileCompletion);
          Preference.setInt(Preference.RESUME_PARSER_DATA_COUNT,
              portfolioState.response!.data.resumeParserDataCount!);

          if (portfolioState.response?.data.portfolioProfile.isNotEmpty ==
              true) {
            Preference.setString(Preference.ABOUT_ME,
                '${portfolioState.response?.data.portfolioProfile.first.aboutMe}');

            Preference.setString(Preference.USER_HEADLINE,
                '${portfolioState.response?.data.portfolioProfile.first.headline}');
            Preference.setString(Preference.LOCATION,
                '${portfolioState.response?.data.portfolioProfile.first.city}, ${portfolioState.response?.data.portfolioProfile.first.country}');
          }
          setState(() {});
          break;

        case ApiStatus.ERROR:
          FirebaseAnalytics.instance
              .logEvent(name: 'splash_portfolio_menu', parameters: {
            "error": portfolioState.error ?? '',
          });
          Log.v(
              "PortfolioState Error..........................${portfolioState.error}");
          FirebaseAnalytics.instance
              .logEvent(name: 'splash_screen', parameters: {
            "ERROR": '${portfolioState.error}',
          });

          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  void _handelBottomNavigationBar(GetBottomBarState state) {
    var getBottomBarState = state;
    setState(() async {
      switch (getBottomBarState.apiState) {
        case ApiStatus.LOADING:
          Log.v("Loading....................");
          break;
        case ApiStatus.SUCCESS:
          Log.v("Success....................");
          menuList = state.response?.data?.menu;
          menuList = menuList!.where((element) {
            bool containRole = element.role.toString().toLowerCase().contains(
                '${Preference.getString(Preference.ROLE)?.toLowerCase()}');
            return containRole;
          }).toList();
          if (menuList?.length == 0) {
            AlertsWidget.alertWithOkBtn(
                context: context,
                text: tr('menu_not_found_msg'),
                onOkClick: () {
                  FocusScope.of(context).unfocus();
                });
          } else {
            // menuList?.sort((a, b) => a.inAppOrder!.compareTo(b.inAppOrder!));
            menuList?.sort((a, b) => int.parse('${a.inAppOrder}')
                .compareTo(int.parse('${b.inAppOrder}')));

            await Future.delayed(Duration(milliseconds: waitFor));
            Navigator.pushAndRemoveUntil(
                context,
                NextPageRoute(
                    homePage(
                      bottomMenu: menuList,
                      index: widget.intialRoute != null
                          ? menuList?.indexWhere(
                              (element) => element.url == widget.intialRoute)
                          : 0,
                      routeWidget: widget.routeWidget,
                    ),
                    isMaintainState: true),
                (route) => false);
          }
          break;

        case ApiStatus.ERROR:
          FirebaseAnalytics.instance
              .logEvent(name: 'splash_bottom_menu', parameters: {
            "error": getBottomBarState.error ?? '',
          });

          ///Use offline mode data
          /*Box? box = Hive.box(DB.CONTENT);
          if (box!.get('bottomMenu') != null) {
            menuList = box
                .get("bottomMenu")
                .map((e) =>
                Menu.fromJson(Map<String, dynamic>.from(e)))
                .cast<Menu>()
                .toList();

                

            // menuList?.sort((a, b) => a.inAppOrder!.compareTo(b.inAppOrder!));
            menuList?.sort((a, b) => (int.tryParse('${a.inAppOrder}') ?? 0).compareTo(int.tryParse('${b.inAppOrder}') ?? 0));

            Navigator.pushAndRemoveUntil(
                context,
                NextPageRoute(
                    homePage(
                      bottomMenu: menuList,
                      index: widget.intialRoute != null
                          ? menuList?.indexWhere(
                              (element) => element.url == widget.intialRoute)
                          : 0,
                      routeWidget: widget.routeWidget,
                    ),
                    isMaintainState: true),
                    (route) => false);

          }*/

          //route in case of any error like invalid token
          Utility.logoutUser(context);
          if (APK_DETAILS["enable_boarding_screen"] == "0") {
            await Future.delayed(Duration(milliseconds: waitFor));
            Navigator.pushAndRemoveUntil(
                context,
                NextPageRoute(SelectLanguage(
                  showEdulystLogo: true,
                )),
                (route) => false);
          } else {
            await Future.delayed(Duration(milliseconds: waitFor));
            Navigator.pushAndRemoveUntil(
                context, NextPageRoute(SingularisWowPreBoarding()), (route) => false);
          }
          break;
        case ApiStatus.INITIAL:
          break;
      }
    });
  }

  Future<void> _moveToNext() async {
    if (Preference.getString(Preference.USER_TOKEN) != null) {
      if (UserSession.userAppLanguageId == 0 ||
          UserSession.userContentLanguageId == 0) {
        getBottomNavigationBar();
      } else {
        getBottomNavigationBar();
      }
    } else {
      if (APK_DETAILS["enable_boarding_screen"] == "0") {
        await Future.delayed(Duration(milliseconds: waitFor));
        Navigator.pushAndRemoveUntil(
            context,
            NextPageRoute(SelectLanguage(
              showEdulystLogo: true,
            )),
            (route) => false);
      } else {
        await Future.delayed(Duration(milliseconds: waitFor));
        Navigator.pushAndRemoveUntil(
            context, NextPageRoute(SingularisWowPreBoarding()), (route) => false);
      }
    }
  }
}

class CustomCard extends StatefulWidget {
  const CustomCard(this.label, {Key? key}) : super(key: key);
  final String label;

  @override
  State<CustomCard> createState() => _CustomCardState();
}

class _CustomCardState extends State<CustomCard> {
  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      child: Transform.scale(
        scale: 30,
        child: Container(
          height: MediaQuery.of(context).size.height,
          width: MediaQuery.of(context).size.height,
          decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
                colors: [
                  ColorConstants().primaryColorGradient(),
                  ColorConstants().primaryColor() ??
                      ColorConstants().primaryColorAlways()
                ],
              ),
              shape: BoxShape.circle,
              color: ColorConstants().primaryColor()),
        ),
      ),
    );
  }
}
